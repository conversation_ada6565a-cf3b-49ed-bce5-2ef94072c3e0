{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?3a22", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?0686", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?399f", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?1f3a", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?a43e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabbar", "data", "tabbarHeight", "contentHeight", "searchKeyword", "onLoad", "methods", "getTabbarHeight", "setTimeout", "uni", "getApp", "console", "onSearch", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyN;AACzN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCkCp4B;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;QACAC;UACA;UACA;UACAC;UACAA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAH;UACAI;UACAC;QACA;QACA;MACA;MAEAH;MACA;MACAF;QACAI;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAA2oD,CAAgB,07CAAG,EAAC,C;;;;;;;;;;;ACA/pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<!-- 搜索栏 -->\r\n\t\t<view class=\"search-container\">\r\n\t\t\t<u-search\r\n\t\t\t\tv-model=\"searchKeyword\"\r\n\t\t\t\tplaceholder=\"搜索牛人技能、职位、公司...\"\r\n\t\t\t\t:height=\"88\"\r\n\t\t\t\t:show-action=\"false\"\r\n\t\t\t\tbg-color=\"#f8f9fa\"\r\n\t\t\t\tborder-color=\"transparent\"\r\n\t\t\t\t@search=\"onSearch\"\r\n\t\t\t\t@custom=\"onSearch\"\r\n\t\t\t></u-search>\r\n\t\t</view>\r\n\r\n\t\t<!-- 牛人列表内容区域 -->\r\n\t\t<view class=\"content\" :style=\"{ paddingBottom: tabbarHeight + 'px' }\">\r\n\t\t\t<!-- 这里可以添加牛人列表内容 -->\r\n\t\t\t<view class=\"talent-list\">\r\n\t\t\t\t<text class=\"placeholder-text\">牛人列表内容待开发...</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 公共TabBar -->\r\n\t\t<view class=\"u-tabbar\">\r\n\t\t\t<custom-tabbar :value=\"0\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport CustomTabbar from '@/components/CustomTabbar.vue'\r\n\r\nexport default {\r\n\tcomponents: { CustomTabbar },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttabbarHeight: 0,\r\n\t\t\tcontentHeight: 0,\r\n\t\t\tsearchKeyword: ''\r\n\t\t}\r\n\t},\r\n\r\n\tonLoad() {\r\n\t\t// 页面加载时获取tabbar高度\r\n\t\tthis.getTabbarHeight()\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// 获取tabbar高度\r\n\t\tgetTabbarHeight() {\r\n\t\t\t// 等待tabbar高度获取完成\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tuni.createSelectorQuery().select('.u-tabbar').boundingClientRect(rect => {\r\n\t\t\t\t\tlet tabbarPx = rect ? rect.height : 0\r\n\t\t\t\t\t// 存储到全局变量\r\n\t\t\t\t\tgetApp().globalData = getApp().globalData || {}\r\n\t\t\t\t\tgetApp().globalData.tabbarHeight = tabbarPx\r\n\t\t\t\t\tconsole.log(getApp().globalData)\r\n\t\t\t\t}).exec()\r\n\t\t\t}, 500) // 延迟300ms获取\r\n\t\t},\r\n\r\n\t\t// 搜索功能\r\n\t\tonSearch() {\r\n\t\t\tif (!this.searchKeyword.trim()) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入搜索关键词',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('搜索关键词:', this.searchKeyword)\r\n\t\t\t// TODO: 实现搜索逻辑\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `搜索: ${this.searchKeyword}`,\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\theight: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbackground-color: #f8f9fa;\r\n}\r\n\r\n.search-container {\r\n\theight: 88rpx;\r\n\tbackground-color: #ffffff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tborder-bottom: 1rpx solid #e9ecef;\r\n\t// 不设置padding，让搜索框占满整个容器\r\n}\r\n\r\n.content {\r\n\tflex: 1;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.talent-list {\r\n\theight: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\r\n\t.placeholder-text {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n}\r\n\r\n.u-tabbar {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 999;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753630876976\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
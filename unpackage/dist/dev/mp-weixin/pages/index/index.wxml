<view class="page data-v-57280228"><view class="search-container data-v-57280228"><u-search vue-id="8dd740cc-1" placeholder="搜索牛人技能、职位、公司..." height="{{88}}" show-action="{{false}}" bg-color="#f8f9fa" border-color="transparent" value="{{searchKeyword}}" data-event-opts="{{[['^search',[['onSearch']]],['^custom',[['onSearch']]],['^input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" class="data-v-57280228" bind:__l="__l"></u-search></view><view class="content data-v-57280228" style="{{'padding-bottom:'+(tabbarHeight+'px')+';'}}"><view class="talent-list data-v-57280228"><text class="placeholder-text data-v-57280228">牛人列表内容待开发...</text></view></view><view class="u-tabbar data-v-57280228"><custom-tabbar vue-id="8dd740cc-2" value="{{0}}" class="data-v-57280228" bind:__l="__l"></custom-tabbar></view></view>
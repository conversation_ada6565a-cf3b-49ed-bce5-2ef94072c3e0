/**
 * 简单的Tabbar高度获取工具函数
 * 直接使用uni.createSelectorQuery()获取高度，供全局使用
 */

/**
 * 获取CustomTabbar高度
 * @returns {number} tabbar高度，如果未获取到返回0
 */
export function getTabbarHeight() {
  // 优先从uni.tabbarHeight获取
  if (uni.tabbarHeight) {
    return uni.tabbarHeight
  }

  // 从globalData获取
  const app = getApp()
  if (app.globalData && app.globalData.tabbarHeight) {
    return app.globalData.tabbarHeight
  }

  // 从uni.$u获取
  if (uni.$u && uni.$u.tabbarHeight) {
    return uni.$u.tabbarHeight
  }

  return 0
}

/**
 * 使用uni.createSelectorQuery()获取任意元素高度
 * @param {string} selector - 选择器，如'.u-tabbar'
 * @param {object} context - 组件上下文，通常是this
 * @param {function} callback - 回调函数，参数为height
 */
export function getElementHeight(selector, context, callback) {
  const query = uni.createSelectorQuery().in(context)
  query.select(selector).boundingClientRect((data) => {
    if (data && callback) {
      callback(data.height)
    }
  }).exec()
}

/**
 * 获取系统信息相关高度
 */
export function getSystemHeights() {
  const systemInfo = uni.getSystemInfoSync()
  return {
    statusBarHeight: systemInfo.statusBarHeight || 0,
    windowHeight: systemInfo.windowHeight,
    screenHeight: systemInfo.screenHeight,
    safeAreaBottom: systemInfo.safeArea ? systemInfo.screenHeight - systemInfo.safeArea.bottom : 0
  }
}

/**
 * 计算内容区域可用高度
 * @param {number} tabbarHeight - tabbar高度
 * @param {number} navbarHeight - 导航栏高度（可选）
 * @returns {number} 内容区域高度
 */
export function getContentHeight(tabbarHeight = 0, navbarHeight = 0) {
  const { windowHeight } = getSystemHeights()
  return windowHeight - tabbarHeight - navbarHeight
}

/**
 * 等待tabbar高度获取完成
 * @param {function} callback - 回调函数
 * @param {number} timeout - 超时时间（毫秒），默认8000ms
 * @param {number} interval - 检查间隔（毫秒），默认200ms
 */
export function waitForTabbarHeight(callback, timeout = 8000, interval = 200) {
  const startTime = Date.now()

  const check = () => {
    const height = getTabbarHeight()
    if (height > 0) {
      callback(height)
      return
    }

    if (Date.now() - startTime > timeout) {
      console.warn('等待tabbar高度超时，可能CustomTabbar组件还未完全加载')
      callback(0)
      return
    }

    setTimeout(check, interval)
  }

  // 首次检查前稍微延迟，给组件更多渲染时间
  setTimeout(check, 500)
}

/**
 * 延迟获取元素高度（推荐用于组件刚挂载时）
 * @param {string} selector - 选择器
 * @param {object} context - 组件上下文
 * @param {function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒），默认300ms
 */
export function getElementHeightDelayed(selector, context, callback, delay = 300) {
  setTimeout(() => {
    context.$nextTick(() => {
      getElementHeight(selector, context, callback)
    })
  }, delay)
}

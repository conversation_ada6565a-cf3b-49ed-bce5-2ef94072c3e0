<template>
	<view class="page">

		<!-- 公共TabBar -->
		<view class="u-tabbar">
			<custom-tabbar :value="0" />
		</view>
	</view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar.vue'

export default {
	components: { CustomTabbar },
	data() {
		return {
			tabbarHeight: 0,
			contentHeight: 0
		}
	},

	onLoad() {
		// 页面加载时获取tabbar高度
		this.getTabbarHeight()
	},

	methods: {
		// 获取tabbar高度
		getTabbarHeight() {
			// 等待tabbar高度获取完成
			setTimeout(() => {
				uni.createSelectorQuery().select('.u-tabbar').boundingClientRect(rect => {
					let tabbarPx = rect ? rect.height : 0
					// 存储到全局变量
					getApp().globalData = getApp().globalData || {}
					getApp().globalData.tabbarHeight = tabbarPx
					console.log(getApp().globalData)
				}).exec()
			}, 500) // 延迟300ms获取
		}
	}
}
</script>

<style lang="scss" scoped>

</style>
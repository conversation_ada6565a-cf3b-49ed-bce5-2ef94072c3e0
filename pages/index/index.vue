<template>
	<view class="page">
		<!-- 搜索栏 -->
		<view class="search-container">
			<u-search
				v-model="searchKeyword"
				placeholder="搜索牛人技能、职位、公司..."
				:height="88"
				:show-action="false"
				bg-color="#f8f9fa"
				border-color="transparent"
				@search="onSearch"
				@custom="onSearch"
			></u-search>
		</view>

		<!-- 牛人列表内容区域 -->
		<view class="content" :style="{ paddingBottom: tabbarHeight + 'px' }">
			<!-- 这里可以添加牛人列表内容 -->
			<view class="talent-list">
				<text class="placeholder-text">牛人列表内容待开发...</text>
			</view>
		</view>

		<!-- 公共TabBar -->
		<view class="u-tabbar">
			<custom-tabbar :value="0" />
		</view>
	</view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar.vue'

export default {
	components: { CustomTabbar },
	data() {
		return {
			tabbarHeight: 0,
			contentHeight: 0,
			searchKeyword: ''
		}
	},

	onLoad() {
		// 页面加载时获取tabbar高度
		this.getTabbarHeight()
	},

	methods: {
		// 获取tabbar高度
		getTabbarHeight() {
			// 等待tabbar高度获取完成
			setTimeout(() => {
				uni.createSelectorQuery().select('.u-tabbar').boundingClientRect(rect => {
					let tabbarPx = rect ? rect.height : 0
					// 存储到全局变量
					getApp().globalData = getApp().globalData || {}
					getApp().globalData.tabbarHeight = tabbarPx
					console.log(getApp().globalData)
				}).exec()
			}, 500) // 延迟300ms获取
		},

		// 搜索功能
		onSearch() {
			if (!this.searchKeyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				})
				return
			}

			console.log('搜索关键词:', this.searchKeyword)
			// TODO: 实现搜索逻辑
			uni.showToast({
				title: `搜索: ${this.searchKeyword}`,
				icon: 'none'
			})
		}
	}
}
</script>

<style lang="scss" scoped></style>